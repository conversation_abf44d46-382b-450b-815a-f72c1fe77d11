function showGameDetail(gameName) {
  document.getElementById("home").style.display = "none"
  document.getElementById("gameDetail").style.display = "block"
  document.getElementById("trainerDetail").style.display = "none"
  document.getElementById("gameTitle").textContent = "下单"
}

function showTrainerDetail(trainerName) {
  document.getElementById("home").style.display = "none"
  document.getElementById("gameDetail").style.display = "none"
  document.getElementById("trainerDetail").style.display = "block"
}

function showHome() {
  document.getElementById("home").style.display = "block"
  document.getElementById("gameDetail").style.display = "none"
  document.getElementById("trainerDetail").style.display = "none"
}

function showMoreGames() {
  alert("更多游戏功能开发中...")
}

function increaseQuantity() {
  const input = document.getElementById("quantity")
  input.value = parseInt(input.value) + 1
  updatePrice()
}

function decreaseQuantity() {
  const input = document.getElementById("quantity")
  if (parseInt(input.value) > 1) {
    input.value = parseInt(input.value) - 1
    updatePrice()
  }
}

function updatePrice() {
  const quantity = document.getElementById("quantity").value
  const priceAmount = document.querySelector(".price-amount")
  if (priceAmount) {
    priceAmount.textContent = `25元 /小时 *${quantity}`
  }
}

function submitOrder() {
  const ageConfirm = document.getElementById("ageConfirm").checked
  if (!ageConfirm) {
    alert("请确认您已成年并同意相关条款")
    return
  }
  alert("订单提交成功！陪练师将尽快联系您。")
}

function playVoice(trainerName) {
  alert(`正在播放 ${trainerName} 的语音试听...`)
}

// 热门标签切换
document.addEventListener("DOMContentLoaded", function () {
  const hotTags = document.querySelectorAll(".hot-tag:not(.more-btn)")
  hotTags.forEach((tag) => {
    tag.addEventListener("click", function () {
      hotTags.forEach((t) => t.classList.remove("active"))
      this.classList.add("active")
    })
  })

  // 性别筛选按钮切换
  const filterBtns = document.querySelectorAll(".filter-btn")
  filterBtns.forEach((btn) => {
    btn.addEventListener("click", function () {
      filterBtns.forEach((b) => b.classList.remove("active"))
      this.classList.add("active")

      const gender = this.getAttribute("data-gender")
      filterTrainersByGender(gender)
    })
  })

  function filterTrainersByGender(gender) {
    const trainerCards = document.querySelectorAll(".trainer-card")
    trainerCards.forEach((card) => {
      const genderIcon = card.querySelector(".gender-icon")
      const isVisible =
        gender === "all" ||
        (gender === "female" && genderIcon && genderIcon.textContent === "♀") ||
        (gender === "male" && (!genderIcon || genderIcon.textContent !== "♀"))

      card.style.display = isVisible ? "block" : "none"
    })
  }

  // 搜索功能
  const searchInput = document.querySelector(".search-input")
  if (searchInput) {
    searchInput.addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        alert(`搜索功能开发中，您搜索的是：${this.value}`)
      }
    })
  }

  // 呼叫陪练师按钮
  const coachBtn = document.querySelector(".coach-btn")
  if (coachBtn) {
    coachBtn.addEventListener("click", function () {
      alert("呼叫陪练师功能开发中...")
    })
  }
})
