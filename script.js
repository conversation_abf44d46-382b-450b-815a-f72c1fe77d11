function showGameDetail(gameName) {
  document.getElementById("home").style.display = "none"
  document.getElementById("gameDetail").style.display = "block"
  document.getElementById("trainerDetail").style.display = "none"
  document.getElementById("gameTitle").textContent = "下单"
}

function showTrainerDetail(trainerName) {
  document.getElementById("home").style.display = "none"
  document.getElementById("gameDetail").style.display = "none"
  document.getElementById("trainerDetail").style.display = "block"
}

function showHome() {
  document.getElementById("home").style.display = "block"
  document.getElementById("gameDetail").style.display = "none"
  document.getElementById("trainerDetail").style.display = "none"
}

function showMoreGames() {
  alert("更多游戏功能开发中...")
}

function increaseQuantity() {
  const input = document.getElementById("quantity")
  input.value = parseInt(input.value) + 1
  updatePrice()
}

function decreaseQuantity() {
  const input = document.getElementById("quantity")
  if (parseInt(input.value) > 1) {
    input.value = parseInt(input.value) - 1
    updatePrice()
  }
}

function updatePrice() {
  const quantity = document.getElementById("quantity").value
  const priceAmount = document.querySelector(".price-amount")
  if (priceAmount) {
    priceAmount.textContent = `25元 /小时 *${quantity}`
  }
}

function submitOrder() {
  const ageConfirm = document.getElementById("ageConfirm").checked
  if (!ageConfirm) {
    alert("请确认您已成年并同意相关条款")
    return
  }
  alert("订单提交成功！陪练师将尽快联系您。")
}

// 热门标签切换
document.addEventListener("DOMContentLoaded", function () {
  const hotTags = document.querySelectorAll(".hot-tag:not(.more-btn)")
  hotTags.forEach((tag) => {
    tag.addEventListener("click", function () {
      hotTags.forEach((t) => t.classList.remove("active"))
      this.classList.add("active")
    })
  })

  // 筛选按钮切换
  const filterBtns = document.querySelectorAll(".filter-btn")
  filterBtns.forEach((btn) => {
    btn.addEventListener("click", function () {
      if (this.textContent.trim() === "性别") {
        alert("性别筛选功能开发中...")
      }
    })
  })

  // 搜索功能
  const searchInput = document.querySelector(".search-input")
  if (searchInput) {
    searchInput.addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        alert(`搜索功能开发中，您搜索的是：${this.value}`)
      }
    })
  }

  // 呼叫陪练师按钮
  const coachBtn = document.querySelector(".coach-btn")
  if (coachBtn) {
    coachBtn.addEventListener("click", function () {
      alert("呼叫陪练师功能开发中...")
    })
  }
})
