function showGameDetail(gameName) {
    document.getElementById('home').style.display = 'none';
    document.getElementById('gameDetail').style.display = 'block';
    document.getElementById('gameTitle').textContent = gameName;
}

function showHome() {
    document.getElementById('home').style.display = 'block';
    document.getElementById('gameDetail').style.display = 'none';
}

// 服务类型切换
document.addEventListener('DOMContentLoaded', function() {
    const serviceItems = document.querySelectorAll('.service-item');
    serviceItems.forEach(item => {
        item.addEventListener('click', function() {
            serviceItems.forEach(i => i.classList.remove('active'));
            this.classList.add('active');
        });
    });
});