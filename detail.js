// 价格配置
const PRICE_PER_HOUR = 25;

// 返回上一页
function goBack() {
  window.history.back();
}

// 播放语音试听
function playVoice(trainerName) {
  alert(`正在播放 ${trainerName} 的语音试听...`);
}

// 增加数量
function increaseQuantity() {
  const quantityInput = document.getElementById('quantity');
  let currentValue = parseInt(quantityInput.value);
  quantityInput.value = currentValue + 1;
  updatePrice();
}

// 减少数量
function decreaseQuantity() {
  const quantityInput = document.getElementById('quantity');
  let currentValue = parseInt(quantityInput.value);
  if (currentValue > 1) {
    quantityInput.value = currentValue - 1;
    updatePrice();
  }
}

// 更新价格显示
function updatePrice() {
  const quantity = parseInt(document.getElementById('quantity').value);
  const totalPrice = PRICE_PER_HOUR * quantity;
  
  document.getElementById('totalPrice').textContent = `¥${totalPrice}`;
  document.getElementById('priceDetail').textContent = 
    `${PRICE_PER_HOUR}元/小时 × ${quantity}小时 = ${totalPrice}元`;
}

// 提交订单
function submitOrder() {
  const ageConfirm = document.getElementById('ageConfirm').checked;
  const quantity = parseInt(document.getElementById('quantity').value);
  const totalPrice = PRICE_PER_HOUR * quantity;
  
  if (!ageConfirm) {
    alert('请确认您已成年并同意相关条款');
    return;
  }
  
  alert(`订单提交成功！\n陪练师：雾眼\n购买时长：${quantity}小时\n总费用：${totalPrice}元\n陪练师将尽快联系您。`);
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
  // 初始化价格显示
  updatePrice();
  
  // 监听数量输入框变化
  const quantityInput = document.getElementById('quantity');
  quantityInput.addEventListener('change', function() {
    if (this.value < 1) {
      this.value = 1;
    }
    updatePrice();
  });
});
