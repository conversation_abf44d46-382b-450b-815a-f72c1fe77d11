* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
  min-height: 100vh;
  color: #e0e6ed;
  line-height: 1.6;
}

.page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
}

/* 顶部搜索栏 */
.header {
  background: rgba(12, 20, 38, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  padding: 1rem;
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-box {
  flex: 1;
  position: relative;
  background: rgba(26, 35, 50, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 25px;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
}

.search-icon {
  color: #00ffff;
  font-size: 1rem;
}

.search-input {
  flex: 1;
  border: none;
  background: none;
  outline: none;
  font-size: 0.9rem;
  color: #e0e6ed;
}

.search-input::placeholder {
  color: #7a8a9a;
}

.coach-btn {
  background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);
  color: #0c1426;
  padding: 0.75rem 1rem;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.5);
}

.coach-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 255, 255, 0.5);
  background: linear-gradient(135deg, #00ffff 0%, #40a0ff 100%);
}

/* 游戏分类网格 */
.game-categories {
  padding: 1.5rem 1rem;
  background: rgba(26, 35, 50, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.2);
  margin: 0.5rem 1rem;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1.5rem;
}

.category-item {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.category-item:hover {
  transform: translateY(-5px);
}

.category-item:active {
  transform: scale(0.95);
}

.game-icon {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 12px;
  margin: 0 auto 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.game-icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s;
}

.category-item:hover .game-icon::before {
  left: 100%;
}

.game-icon.wzry {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}
.game-icon.sjzxd {
  background: linear-gradient(135deg, #4834d4, #686de0);
}
.game-icon.hpjy {
  background: linear-gradient(135deg, #00d2d3, #54a0ff);
}
.game-icon.lol {
  background: linear-gradient(135deg, #feca57, #ff9ff3);
}
.game-icon.wwqy {
  background: linear-gradient(135deg, #ff3838, #ff6348);
}
.game-icon.jdqs {
  background: linear-gradient(135deg, #2c2c54, #40407a);
}
.game-icon.yxlm {
  background: linear-gradient(135deg, #0abde3, #006ba6);
}
.game-icon.csgo {
  background: linear-gradient(135deg, #ff9500, #ff6348);
}
.game-icon.yjwj {
  background: linear-gradient(135deg, #8e44ad, #9c88ff);
}
.game-icon.more {
  background: linear-gradient(135deg, #ddd, #bbb);
}

.category-item span {
  font-size: 0.85rem;
  color: #e0e6ed;
  font-weight: 500;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* 热门游戏标签 */
.hot-games {
  padding: 0 1rem 1rem;
}

.hot-tags {
  display: flex;
  gap: 0.75rem;
  overflow-x: auto;
  padding: 0.5rem 0;
}

.hot-tag {
  background: rgba(26, 35, 50, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.3s;
  color: #e0e6ed;
  backdrop-filter: blur(10px);
}

.hot-tag:hover {
  border-color: rgba(0, 255, 255, 0.6);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.hot-tag.active {
  background: linear-gradient(135deg, #00ffff, #0080ff);
  color: #0c1426;
  border-color: #00ffff;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.hot-tag.more-btn {
  background: rgba(26, 35, 50, 0.6);
  color: #7a8a9a;
  border-color: rgba(122, 138, 154, 0.3);
}

/* 推荐大神 */
.recommended {
  padding: 1rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #e0e6ed;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.filter-buttons {
  display: flex;
  gap: 0.5rem;
  background: rgba(26, 35, 50, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 20px;
  padding: 0.25rem;
  backdrop-filter: blur(10px);
}

.filter-btn {
  background: transparent;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 15px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s;
  color: #7a8a9a;
  min-width: 50px;
}

.filter-btn:hover {
  color: #00ffff;
}

.filter-btn.active {
  background: linear-gradient(135deg, #00ffff, #0080ff);
  color: #0c1426;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.trainer-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* 陪练师卡片 */
.trainer-card {
  background: rgba(26, 35, 50, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 15px;
  padding: 1rem;
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.trainer-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 255, 255, 0.05) 0%,
    rgba(0, 128, 255, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s;
}

.trainer-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 255, 255, 0.2);
  border-color: rgba(0, 255, 255, 0.5);
}

.trainer-card:hover::before {
  opacity: 1;
}

.trainer-card.premium {
  border: 2px solid #ffd700;
  background: rgba(255, 215, 0, 0.1);
  box-shadow: 0 4px 20px rgba(255, 215, 0, 0.2);
}

.trainer-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 0.75rem;
  position: relative;
  z-index: 1;
}

.trainer-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  border: 2px solid rgba(0, 255, 255, 0.5);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.trainer-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.online-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  background: #00ff00;
  border: 2px solid #0c1426;
  border-radius: 50%;
  box-shadow: 0 0 10px #00ff00;
}

.trainer-basic {
  flex: 1;
  min-width: 0;
}

.name-badges {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.name-badges h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #e0e6ed;
  margin: 0;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.game-tag {
  font-size: 0.8rem;
  color: #00ffff;
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
}

.gender-icon {
  font-size: 1rem;
  color: #ff69b4;
  text-shadow: 0 0 10px rgba(255, 105, 180, 0.5);
}

.badges {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-weight: 500;
}

.badge.verified {
  background: #e3f2fd;
  color: #1976d2;
}

.badge.hot {
  background: #fff3e0;
  color: #f57c00;
}

.voice-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 20px;
  padding: 0.4rem 0.8rem;
  color: #00ffff;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.voice-info::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: linear-gradient(to bottom, #00ffff, #0080ff);
  border-radius: 2px;
}

.voice-info:hover {
  background: rgba(0, 255, 255, 0.2);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
  transform: scale(1.05);
}

.voice-info i {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.trainer-desc {
  color: #b0c4de;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 0.75rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.trainer-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.price {
  font-size: 1.2rem;
  font-weight: 700;
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.unit {
  font-size: 0.85rem;
  color: #7a8a9a;
}

/* 详情页头部 */
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(12, 20, 38, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  background: none;
  border: 1px solid rgba(0, 255, 255, 0.3);
  font-size: 1.2rem;
  color: #00ffff;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s;
}

.back-btn:hover {
  background: rgba(0, 255, 255, 0.1);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #e0e6ed;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.function-intro {
  font-size: 0.9rem;
  color: #00ffff;
  cursor: pointer;
  transition: all 0.3s;
}

.function-intro:hover {
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* 陪练师资料 */
.trainer-profile {
  background: rgba(26, 35, 50, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.2);
  padding: 1.5rem;
  margin: 1rem;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  text-align: center;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #00ffff;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-info h2 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #e0e6ed;
  margin-bottom: 0.5rem;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.voice-sample {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: #00ffff;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all 0.3s;
}

.voice-sample:hover {
  background: rgba(0, 255, 255, 0.2);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.profile-desc {
  color: #b0c4de;
  line-height: 1.6;
  text-align: left;
}

/* 订单表单 */
.order-form {
  background: rgba(26, 35, 50, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.2);
  margin: 1rem;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.form-group {
  padding: 1rem;
  border-bottom: 1px solid rgba(0, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-group:last-child {
  border-bottom: none;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #e0e6ed;
  font-weight: 500;
}

.game-brand-icon {
  color: #00ffff;
}

.info-icon {
  color: #7a8a9a;
  font-size: 0.8rem;
}

.input-placeholder {
  color: #7a8a9a;
  font-size: 0.9rem;
}

.quantity-group {
  flex-direction: column;
  align-items: stretch;
}

.quantity-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 0.5rem;
}

.quantity-btn {
  width: 40px;
  height: 40px;
  border: 1px solid rgba(0, 255, 255, 0.3);
  background: rgba(0, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  color: #00ffff;
  transition: all 0.3s;
}

.quantity-btn:hover {
  background: rgba(0, 255, 255, 0.2);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.quantity-input {
  width: 60px;
  text-align: center;
  border: 1px solid rgba(0, 255, 255, 0.3);
  background: rgba(0, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.5rem;
  font-size: 1rem;
  color: #e0e6ed;
}

.unit {
  font-size: 0.9rem;
  color: #7a8a9a;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #00ffff;
}

.checkbox-group label {
  font-size: 0.85rem;
  color: #b0c4de;
  cursor: pointer;
}

.note-input {
  width: 100%;
  min-height: 80px;
  border: 1px solid rgba(0, 255, 255, 0.3);
  background: rgba(0, 255, 255, 0.05);
  border-radius: 8px;
  padding: 0.75rem;
  font-size: 0.9rem;
  resize: vertical;
  font-family: inherit;
  color: #e0e6ed;
}

.note-input::placeholder {
  color: #7a8a9a;
}

.price-summary {
  padding: 1rem;
  background: rgba(0, 255, 255, 0.05);
  border-top: 1px solid rgba(0, 255, 255, 0.2);
}

.total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-label {
  font-size: 0.9rem;
  color: #7a8a9a;
}

.price-amount {
  font-size: 1.1rem;
  font-weight: 600;
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.order-btn {
  width: 100%;
  background: linear-gradient(135deg, #00ffff, #0080ff);
  color: #0c1426;
  border: none;
  padding: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.3);
}

.order-btn:hover {
  background: linear-gradient(135deg, #40a0ff, #00ffff);
  box-shadow: 0 6px 30px rgba(0, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* 陪练师详情页 */
.trainer-detail-content {
  padding: 1rem;
}

.trainer-card.detailed {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.trainer-avatar.large {
  width: 100px;
  height: 100px;
  margin-bottom: 1rem;
}

.online-status.large {
  width: 16px;
  height: 16px;
  bottom: 5px;
  right: 5px;
}

.trainer-details {
  width: 100%;
}

.name-rating {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.name-rating h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #ffd700;
}

.rating-score {
  margin-left: 0.5rem;
  color: #666;
  font-size: 0.9rem;
}

.rank,
.specialty,
.games {
  color: #666;
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.price-book {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #f0f0f0;
}

.book-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.book-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.trainer-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.85rem;
  color: #666;
}

.trainer-description {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.trainer-description h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.trainer-description p {
  color: #666;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .category-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .trainer-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    padding: 1rem;
  }

  .stat-number {
    font-size: 1.2rem;
  }
}

@media (min-width: 768px) {
  .category-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}
