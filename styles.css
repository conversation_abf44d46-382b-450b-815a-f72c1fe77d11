* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background: linear-gradient(135deg, #f5f5f5 0%, #fafafa 100%);
  min-height: 100vh;
  color: #333;
  line-height: 1.6;
}

.page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f5f5 0%, #fafafa 100%);
}

/* 顶部搜索栏 */
.header {
  background: white;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-box {
  flex: 1;
  position: relative;
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 25px;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s;
}

.search-box:focus-within {
  border-color: #ff6600;
  box-shadow: 0 0 0 2px rgba(255, 102, 0, 0.1);
}

.search-icon {
  color: #999;
  font-size: 1rem;
}

.search-input {
  flex: 1;
  border: none;
  background: none;
  outline: none;
  font-size: 0.9rem;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.coach-btn {
  background: linear-gradient(135deg, #ff6600 0%, #ff8533 100%);
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 15px rgba(255, 102, 0, 0.3);
  border: none;
}

.coach-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(255, 102, 0, 0.4);
  background: linear-gradient(135deg, #ff8533 0%, #ff6600 100%);
}

/* 游戏分类网格 */
.game-categories {
  padding: 1.5rem;
  background: white;
  margin: 0.5rem 1rem;
  border-radius: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1.5rem;
  justify-items: center;
}

.category-item {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.category-item:active {
  transform: scale(0.95);
}

.game-icon {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 12px;
  margin: 0 auto 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.game-icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s;
}

.category-item:hover .game-icon::before {
  left: 100%;
}

.game-icon.wzry {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}
.game-icon.sjzxd {
  background: linear-gradient(135deg, #4834d4, #686de0);
}
.game-icon.hpjy {
  background: linear-gradient(135deg, #00d2d3, #54a0ff);
}
.game-icon.lol {
  background: linear-gradient(135deg, #feca57, #ff9ff3);
}
.game-icon.wwqy {
  background: linear-gradient(135deg, #ff3838, #ff6348);
}
.game-icon.jdqs {
  background: linear-gradient(135deg, #2c2c54, #40407a);
}
.game-icon.yxlm {
  background: linear-gradient(135deg, #0abde3, #006ba6);
}
.game-icon.csgo {
  background: linear-gradient(135deg, #ff9500, #ff6348);
}
.game-icon.yjwj {
  background: linear-gradient(135deg, #8e44ad, #9c88ff);
}
.game-icon.more {
  background: linear-gradient(135deg, #ddd, #bbb);
}

.category-item span {
  font-size: 0.85rem;
  color: #333;
  font-weight: 500;
}

/* 热门游戏标签 */
.hot-games {
  padding: 0 1rem 1rem;
}

.hot-tags {
  display: flex;
  gap: 0.75rem;
  overflow-x: auto;
  padding: 0.5rem 0;
}

.hot-tag {
  background: white;
  border: 1px solid #e0e0e0;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.3s;
  color: #333;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.hot-tag:hover {
  border-color: #ff6600;
  box-shadow: 0 4px 8px rgba(255, 102, 0, 0.1);
}

.hot-tag.active {
  background: linear-gradient(135deg, #ff6600, #ff8533);
  color: white;
  border-color: #ff6600;
  box-shadow: 0 4px 12px rgba(255, 102, 0, 0.3);
}

.hot-tag.more-btn {
  background: #f5f5f5;
  color: #999;
  border-color: #e0e0e0;
}

/* 推荐大神 */
.recommended {
  padding: 1rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.filter-buttons {
  display: flex;
  gap: 0.25rem;
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  padding: 0.25rem;
}

.filter-btn {
  background: transparent;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 15px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s;
  color: #666;
  min-width: 50px;
  font-weight: 500;
}

.filter-btn:hover {
  color: #ff6600;
  background: rgba(255, 102, 0, 0.05);
}

.filter-btn.active {
  background: linear-gradient(135deg, #ff6600, #ff8533);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 102, 0, 0.3);
}

.trainer-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* 陪练师卡片 */
.trainer-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 15px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.trainer-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #ff6600;
}

.trainer-card.premium {
  border: 2px solid #ffd700;
  background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
  box-shadow: 0 4px 20px rgba(255, 215, 0, 0.2);
}

.trainer-card.premium::before {
  content: "VIP";
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: bold;
  z-index: 2;
}

.trainer-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
  position: relative;
  z-index: 1;
}

.trainer-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  border: 3px solid #ff6600;
  box-shadow: 0 4px 15px rgba(255, 102, 0, 0.2);
}

.trainer-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.online-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  background: #52c41a;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.trainer-basic {
  flex: 1;
  min-width: 0;
}

.name-badges {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.name-badges h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.game-tag {
  font-size: 0.8rem;
  color: #666;
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
}

.gender-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 0.9rem;
  font-weight: bold;
  margin-left: 0.5rem;
  position: relative;
}

.gender-icon[data-gender="female"] {
  background: linear-gradient(135deg, #ff69b4, #ff1493);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 105, 180, 0.3);
}

.gender-icon[data-gender="female"]::before {
  content: "♀";
}

.gender-icon[data-gender="male"] {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: white;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.gender-icon[data-gender="male"]::before {
  content: "♂";
}

.badges {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-weight: 500;
}

.badge.verified {
  background: #e3f2fd;
  color: #1976d2;
}

.badge.hot {
  background: #fff3e0;
  color: #f57c00;
}

.voice-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #fff2e8, #fff7f0);
  border: 1px solid #ff6600;
  border-radius: 20px;
  padding: 0.4rem 0.8rem;
  color: #ff6600;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(255, 102, 0, 0.1);
}

.voice-info::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: linear-gradient(to bottom, #ff6600, #ff8533);
  border-radius: 2px;
}

.voice-info:hover {
  background: linear-gradient(135deg, #ffe7d3, #fff2e8);
  box-shadow: 0 4px 15px rgba(255, 102, 0, 0.2);
  transform: scale(1.05);
}

.voice-info i {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

.trainer-desc {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 0.75rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.trainer-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.price-info {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.price {
  font-size: 1.2rem;
  font-weight: 700;
  color: #ff6600;
}

.unit {
  font-size: 0.85rem;
  color: #999;
}

.footer-badges {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .category-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .trainer-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    padding: 1rem;
  }

  .stat-number {
    font-size: 1.2rem;
  }
}

@media (min-width: 768px) {
  .category-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}
