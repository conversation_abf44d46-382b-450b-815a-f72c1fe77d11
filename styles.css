* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background: #f5f6fa;
  min-height: 100vh;
  color: #333;
  line-height: 1.6;
}

.page {
  min-height: 100vh;
  background: #f5f6fa;
}

/* 顶部搜索栏 */
.header {
  background: white;
  padding: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-box {
  flex: 1;
  position: relative;
  background: #f8f9fa;
  border-radius: 25px;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.search-icon {
  color: #999;
  font-size: 1rem;
}

.search-input {
  flex: 1;
  border: none;
  background: none;
  outline: none;
  font-size: 0.9rem;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.coach-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.coach-btn:hover {
  transform: translateY(-2px);
}

/* 游戏分类网格 */
.game-categories {
  padding: 1.5rem 1rem;
  background: white;
  margin: 0.5rem 1rem;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1.5rem;
}

.category-item {
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s;
}

.category-item:active {
  transform: scale(0.95);
}

.game-icon {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 12px;
  margin: 0 auto 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.game-icon.wzry {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}
.game-icon.sjzxd {
  background: linear-gradient(135deg, #4834d4, #686de0);
}
.game-icon.hpjy {
  background: linear-gradient(135deg, #00d2d3, #54a0ff);
}
.game-icon.lol {
  background: linear-gradient(135deg, #feca57, #ff9ff3);
}
.game-icon.wwqy {
  background: linear-gradient(135deg, #ff3838, #ff6348);
}
.game-icon.jdqs {
  background: linear-gradient(135deg, #2c2c54, #40407a);
}
.game-icon.yxlm {
  background: linear-gradient(135deg, #0abde3, #006ba6);
}
.game-icon.csgo {
  background: linear-gradient(135deg, #ff9500, #ff6348);
}
.game-icon.yjwj {
  background: linear-gradient(135deg, #8e44ad, #9c88ff);
}
.game-icon.more {
  background: linear-gradient(135deg, #ddd, #bbb);
}

.category-item span {
  font-size: 0.85rem;
  color: #333;
  font-weight: 500;
}

/* 热门游戏标签 */
.hot-games {
  padding: 0 1rem 1rem;
}

.hot-tags {
  display: flex;
  gap: 0.75rem;
  overflow-x: auto;
  padding: 0.5rem 0;
}

.hot-tag {
  background: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #eee;
}

.hot-tag.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
}

.hot-tag.more-btn {
  background: #f8f9fa;
  color: #666;
}

/* 推荐大神 */
.recommended {
  padding: 1rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.filter-buttons {
  display: flex;
  gap: 0.5rem;
}

.filter-btn {
  background: white;
  border: 1px solid #eee;
  padding: 0.5rem 0.75rem;
  border-radius: 15px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.trainer-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* 陪练师卡片 */
.trainer-card {
  background: white;
  border-radius: 15px;
  padding: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
}

.trainer-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.trainer-card.premium {
  border: 2px solid #ffd700;
  background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
}

.trainer-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.trainer-avatar {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.trainer-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.online-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #4caf50;
  border: 2px solid white;
  border-radius: 50%;
}

.trainer-basic {
  flex: 1;
  min-width: 0;
}

.name-badges {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.name-badges h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.game-tag {
  font-size: 0.8rem;
  color: #666;
  background: #f0f0f0;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
}

.gender-icon {
  font-size: 1rem;
  color: #ff6b9d;
}

.badges {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-weight: 500;
}

.badge.verified {
  background: #e3f2fd;
  color: #1976d2;
}

.badge.hot {
  background: #fff3e0;
  color: #f57c00;
}

.voice-info {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #666;
  font-size: 0.85rem;
}

.trainer-desc {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 0.75rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.trainer-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.price {
  font-size: 1.2rem;
  font-weight: 700;
  color: #ff6b6b;
}

.unit {
  font-size: 0.85rem;
  color: #666;
}

.play-btn {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
}

.play-btn:hover {
  transform: scale(1.1);
}

/* 详情页头部 */
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #667eea;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background 0.2s;
}

.back-btn:hover {
  background: #f0f0f0;
}

.title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.function-intro {
  font-size: 0.9rem;
  color: #667eea;
  cursor: pointer;
}

/* 陪练师资料 */
.trainer-profile {
  background: white;
  padding: 1.5rem;
  margin: 1rem;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #667eea;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-info h2 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.voice-sample {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #f0f0f0;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.profile-desc {
  color: #666;
  line-height: 1.6;
  text-align: left;
}

/* 订单表单 */
.order-form {
  background: white;
  margin: 1rem;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.form-group {
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-group:last-child {
  border-bottom: none;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #333;
  font-weight: 500;
}

.game-brand-icon {
  color: #ff6b6b;
}

.info-icon {
  color: #999;
  font-size: 0.8rem;
}

.input-placeholder {
  color: #999;
  font-size: 0.9rem;
}

.quantity-group {
  flex-direction: column;
  align-items: stretch;
}

.quantity-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 0.5rem;
}

.quantity-btn {
  width: 40px;
  height: 40px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  color: #333;
  transition: all 0.2s;
}

.quantity-btn:hover {
  background: #f0f0f0;
}

.quantity-input {
  width: 60px;
  text-align: center;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 0.5rem;
  font-size: 1rem;
}

.unit {
  font-size: 0.9rem;
  color: #666;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
  width: 18px;
  height: 18px;
}

.checkbox-group label {
  font-size: 0.85rem;
  color: #666;
  cursor: pointer;
}

.note-input {
  width: 100%;
  min-height: 80px;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 0.75rem;
  font-size: 0.9rem;
  resize: vertical;
  font-family: inherit;
}

.price-summary {
  padding: 1rem;
  background: #f8f9fa;
  border-top: 1px solid #eee;
}

.total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-label {
  font-size: 0.9rem;
  color: #666;
}

.price-amount {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ff6b6b;
}

.order-btn {
  width: 100%;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  padding: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.order-btn:hover {
  background: linear-gradient(135deg, #ee5a24, #ff6b6b);
}

/* 陪练师详情页 */
.trainer-detail-content {
  padding: 1rem;
}

.trainer-card.detailed {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.trainer-avatar.large {
  width: 100px;
  height: 100px;
  margin-bottom: 1rem;
}

.online-status.large {
  width: 16px;
  height: 16px;
  bottom: 5px;
  right: 5px;
}

.trainer-details {
  width: 100%;
}

.name-rating {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.name-rating h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #ffd700;
}

.rating-score {
  margin-left: 0.5rem;
  color: #666;
  font-size: 0.9rem;
}

.rank,
.specialty,
.games {
  color: #666;
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.price-book {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #f0f0f0;
}

.book-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.book-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.trainer-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.85rem;
  color: #666;
}

.trainer-description {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.trainer-description h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.trainer-description p {
  color: #666;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .category-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .trainer-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    padding: 1rem;
  }

  .stat-number {
    font-size: 1.2rem;
  }
}

@media (min-width: 768px) {
  .category-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}
