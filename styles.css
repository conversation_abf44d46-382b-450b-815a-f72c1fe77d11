* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.page {
    min-height: 100vh;
    background: #f8f9fa;
}

/* 头部导航 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    position: sticky;
    top: 0;
    z-index: 100;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.back-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #667eea;
    cursor: pointer;
}

.avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    display: block;
}

/* 轮播图 */
.banner {
    height: 200px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
}

.banner-content h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* 游戏分类 */
.game-categories {
    padding: 1.5rem 1rem;
    background: white;
    margin-top: -1rem;
    border-radius: 1rem 1rem 0 0;
}

.game-categories h3 {
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
}

.category-item {
    text-align: center;
    cursor: pointer;
    transition: transform 0.2s;
}

.category-item:active {
    transform: scale(0.95);
}

.game-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.5rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    margin: 0 auto 0.5rem;
}

/* 推荐陪练师 */
.recommended {
    padding: 1.5rem 1rem;
    background: white;
    margin-top: 0.5rem;
}

.trainer-card {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.75rem;
    margin-bottom: 0.75rem;
}

.trainer-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    margin-right: 1rem;
}

.trainer-info h4 {
    margin-bottom: 0.25rem;
}

.trainer-info p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.price {
    color: #667eea;
    font-weight: bold;
}

/* 游戏详情页 */
.game-info {
    background: white;
}

.game-banner {
    height: 150px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.service-types {
    display: flex;
    padding: 1rem;
    gap: 1rem;
}

.service-item {
    padding: 0.5rem 1rem;
    border-radius: 1.5rem;
    background: #f0f0f0;
    cursor: pointer;
    transition: all 0.2s;
}

.service-item.active {
    background: #667eea;
    color: white;
}

/* 详细陪练师卡片 */
.trainers {
    padding: 1rem;
}

.trainer-card.detailed {
    flex-direction: column;
    align-items: stretch;
    padding: 1.5rem;
}

.trainer-details {
    width: 100%;
    margin-top: 1rem;
}

.name-rating {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.rating {
    color: #ffd700;
}

.specialty {
    color: #666;
    margin: 0.5rem 0;
}

.price-book {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.book-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 1.5rem;
    cursor: pointer;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 375px) {
    .category-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .banner-content h2 {
        font-size: 1.5rem;
    }
}

@media (min-width: 768px) {
    .category-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}