* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background: linear-gradient(135deg, #f5f5f5 0%, #fafafa 100%);
  min-height: 100vh;
  color: #333;
  line-height: 1.6;
}

.page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f5f5 0%, #fafafa 100%);
}

/* 详情页头部 */
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  background: none;
  border: 1px solid #e0e0e0;
  font-size: 1.2rem;
  color: #666;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s;
}

.back-btn:hover {
  background: #f5f5f5;
  border-color: #ff6600;
  color: #ff6600;
}

.title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.function-intro {
  font-size: 0.9rem;
  color: #ff6600;
  cursor: pointer;
  transition: all 0.3s;
}

.function-intro:hover {
  color: #ff8533;
}

/* 陪练师资料 */
.trainer-profile {
  background: white;
  border: 1px solid #e0e0e0;
  padding: 1.5rem;
  margin: 1rem;
  border-radius: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  align-items: flex-start;
}

.profile-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #ff6600;
  box-shadow: 0 4px 15px rgba(255, 102, 0, 0.2);
  flex-shrink: 0;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.online-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  background: #52c41a;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile-info {
  flex: 1;
}

.name-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.name-section h2 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.gender-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 0.9rem;
  font-weight: bold;
}

.gender-icon[data-gender="male"] {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: white;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.gender-icon[data-gender="male"]::before {
  content: "♂";
}

.service-badges {
  display: flex;
  gap: 0.5rem;
}

.badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge.verified {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.badge.hot {
  background: #fff2e8;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.service-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.service-time i {
  color: #ff6600;
}

.voice-sample {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #fff2e8, #fff7f0);
  border: 1px solid #ff6600;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: #ff6600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(255, 102, 0, 0.1);
}

.voice-sample:hover {
  background: linear-gradient(135deg, #ffe7d3, #fff2e8);
  box-shadow: 0 4px 15px rgba(255, 102, 0, 0.2);
}

.voice-sample i {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

.profile-desc h3 {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 1rem;
  border-bottom: 2px solid #ff6600;
  padding-bottom: 0.5rem;
}

.intro-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.skill-section,
.hero-section,
.feature-section {
  margin-bottom: 1rem;
}

.skill-section h4,
.hero-section h4,
.feature-section h4 {
  font-size: 0.9rem;
  color: #333;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.skill-tags,
.hero-tags,
.feature-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.skill-tag {
  background: linear-gradient(135deg, #e6f7ff, #f0f9ff);
  color: #1890ff;
  border: 1px solid #91d5ff;
  padding: 0.3rem 0.6rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.hero-tag {
  background: linear-gradient(135deg, #fff7e6, #fffbf0);
  color: #fa8c16;
  border: 1px solid #ffd591;
  padding: 0.3rem 0.6rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.feature-tag {
  background: linear-gradient(135deg, #f6ffed, #fcffe6);
  color: #52c41a;
  border: 1px solid #b7eb8f;
  padding: 0.3rem 0.6rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.feature-tag i {
  font-size: 0.7rem;
}

/* 订单表单 */
.order-form {
  background: white;
  border: 1px solid #e0e0e0;
  margin: 1rem;
  border-radius: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-group {
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-group:last-child {
  border-bottom: none;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #333;
  font-weight: 500;
}

.game-brand-icon {
  color: #ff6600;
}

.info-icon {
  color: #999;
  font-size: 0.8rem;
}

.game-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #333;
  font-weight: 500;
}

.selected-game {
  color: #ff6600;
}

.input-placeholder {
  color: #999;
  font-size: 0.9rem;
}

.quantity-group {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.quantity-btn {
  width: 40px;
  height: 40px;
  border: 1px solid #e0e0e0;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  color: #666;
  transition: all 0.3s;
}

.quantity-btn:hover {
  background: #f5f5f5;
  border-color: #ff6600;
  color: #ff6600;
}

.quantity-input {
  width: 60px;
  text-align: center;
  border: 1px solid #e0e0e0;
  background: white;
  border-radius: 8px;
  padding: 0.5rem;
  font-size: 1rem;
  color: #333;
}

.unit {
  font-size: 0.9rem;
  color: #999;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #ff6600;
}

.checkbox-group label {
  font-size: 0.85rem;
  color: #666;
  cursor: pointer;
}

.note-group {
  flex-direction: column;
  align-items: stretch;
}

.note-input {
  width: 100%;
  min-height: 80px;
  border: 1px solid #e0e0e0;
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  font-size: 0.9rem;
  resize: vertical;
  font-family: inherit;
  color: #333;
  margin-top: 0.5rem;
}

.note-input::placeholder {
  color: #999;
}

.note-input:focus {
  border-color: #ff6600;
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 102, 0, 0.1);
}

.price-summary {
  padding: 1rem;
  background: #fff7f0;
  border-top: 1px solid #f0f0f0;
}

.total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.price-label {
  font-size: 0.9rem;
  color: #666;
}

.price-amount {
  font-size: 1.3rem;
  font-weight: 700;
  color: #ff6600;
}

.price-detail {
  font-size: 0.8rem;
  color: #999;
  text-align: right;
}

.order-btn {
  width: 100%;
  background: linear-gradient(135deg, #ff6600, #ff8533);
  color: white;
  border: none;
  padding: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 20px rgba(255, 102, 0, 0.3);
}

.order-btn:hover {
  background: linear-gradient(135deg, #ff8533, #ff6600);
  box-shadow: 0 6px 30px rgba(255, 102, 0, 0.4);
  transform: translateY(-2px);
}
